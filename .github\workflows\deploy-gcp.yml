name: Deploy to Google Cloud Platform

on:
  push:
    branches:
      - main
      - master
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  REGION: us-west1
  ENVIRONMENT: ${{ github.event.inputs.environment || 'production' }}

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    permissions:
      contents: read
      id-token: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.PROJECT_ID }}

    - name: Configure Docker for GCR
      run: gcloud auth configure-docker

    - name: Create secrets if they don't exist
      run: |
        # Create secret key if it doesn't exist
        if ! gcloud secrets describe supplyline-secret-key >/dev/null 2>&1; then
          echo -n "${{ secrets.SECRET_KEY }}" | gcloud secrets create supplyline-secret-key --data-file=-
        fi
        
        # Create database credentials if they don't exist
        if ! gcloud secrets describe supplyline-db-username >/dev/null 2>&1; then
          echo -n "${{ secrets.DB_USERNAME }}" | gcloud secrets create supplyline-db-username --data-file=-
        fi
        
        if ! gcloud secrets describe supplyline-db-password >/dev/null 2>&1; then
          echo -n "${{ secrets.DB_PASSWORD }}" | gcloud secrets create supplyline-db-password --data-file=-
        fi

    - name: Submit build to Cloud Build
      run: |
        gcloud builds submit \
          --config=cloudbuild.yaml \
          --substitutions=_REGION=${{ env.REGION }},_CLOUDSQL_INSTANCE=${{ env.PROJECT_ID }}:${{ env.REGION }}:supplyline-db \
          .

    - name: Get service URLs
      id: get-urls
      run: |
        BACKEND_URL=$(gcloud run services describe "supplyline-backend-${{ env.ENVIRONMENT }}" --region="${{ env.REGION }}" --format="value(status.url)")
        FRONTEND_URL=$(gcloud run services describe "supplyline-frontend-${{ env.ENVIRONMENT }}" --region="${{ env.REGION }}" --format="value(status.url)")

        echo "backend-url=$BACKEND_URL" >> $GITHUB_OUTPUT
        echo "frontend-url=$FRONTEND_URL" >> $GITHUB_OUTPUT

    - name: Run smoke tests
      run: |
        # Wait for services to be ready
        sleep 30

        # Test backend health endpoint
        curl -f "${{ steps.get-urls.outputs.backend-url }}/api/health" || exit 1

        # Test frontend
        curl -f "${{ steps.get-urls.outputs.frontend-url }}" || exit 1

    - name: Create deployment summary
      run: |
        {
          echo "## Deployment Summary"
          echo ""
          echo "**Environment:** ${{ env.ENVIRONMENT }}"
          echo "**Region:** ${{ env.REGION }}"
          echo ""
          echo "### Service URLs"
          echo "- **Frontend:** ${{ steps.get-urls.outputs.frontend-url }}"
          echo "- **Backend:** ${{ steps.get-urls.outputs.backend-url }}"
          echo ""
          echo "### Admin Credentials"
          echo "- **Username:** ADMIN001"
          echo "- **Password:** Generated during deployment (check deployment logs)"
          echo "- **⚠️ Important:** Change password after first login"
        } >> $GITHUB_STEP_SUMMARY

    - name: Notify on failure
      if: failure()
      run: |
        echo "## Deployment Failed" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "The deployment to ${{ env.ENVIRONMENT }} environment failed." >> $GITHUB_STEP_SUMMARY
        echo "Please check the logs for more details." >> $GITHUB_STEP_SUMMARY
